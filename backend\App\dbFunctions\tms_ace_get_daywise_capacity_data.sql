CREATE OR REPLACE FUNCTION public.tms_ace_get_daywise_capacity_data(form_data json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    status bool;
    message text;
    resp_data json;
    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;
    city_name_ text;
    start_date_ date;
    end_date_ date;
    current_date_ date;
    days_data json[];
    single_day_data json;
    day_name text;
    formatted_date text;
    total_slots integer;
    available_slots integer;
    total_capacity integer;
    available_capacity integer;
    day_counter integer;
    vertical_id_ int;
    service_prvdr_ int;
BEGIN
    status = false;
    message = 'Internal_error';
    resp_data = '[]'::json;

    -- Extract parameters from form_data
    org_id_ = (form_data->>'org_id')::integer;
    usr_id_ = form_data->>'usr_id';
    ip_address_ = form_data->>'ip_address';
    user_agent_ = form_data->>'user_agent';
    city_name_ = form_data->>'city_name';
    vertical_id_ = form_data->>'vertical_id';
    service_prvdr_ = form_data->>'service_prvdr';


    -- Set date range for next 7 days starting from today
    start_date_ = CURRENT_DATE;
    end_date_ = CURRENT_DATE + INTERVAL '6 days';
    
    -- Initialize array for days data
    days_data = ARRAY[]::json[];
    day_counter = 0;

    -- Loop through each day for the next 7 days
    FOR current_date_ IN SELECT generate_series(start_date_, end_date_, '1 day'::interval)::date
    LOOP
        day_counter = day_counter + 1;
        
        -- Get day name
        day_name = TO_CHAR(current_date_, 'Day');
        day_name = TRIM(day_name);
        
        -- Format date
        formatted_date = TO_CHAR(current_date_, 'Month DD, YYYY');
        
        -- Get capacity data for this city and date
        SELECT 
            COALESCE(COUNT(DISTINCT cap.start_time), 0),
            COALESCE(SUM(cap.total_capacity), 0),
            COALESCE(SUM(cap.available_capacity), 0)
          INTO 
            total_slots,
            total_capacity,
            available_capacity
          FROM public.cl_tx_capacity cap
         INNER JOIN public.cl_tx_vertical_srvc_hubs hub 
            ON hub.org_id = service_prvdr_
           AND hub.is_active = true        
           AND cap.resource_id LIKE service_prvdr_::text || '_' || vertical_id_::text || '_%_' || hub.id::text
         WHERE cap.usr_tmzone_day = current_date_;
        
        -- If no data found, set default values
        IF total_slots IS NULL THEN
            total_slots = 0;
        END IF;
        IF total_capacity IS NULL THEN
            total_capacity = 0;
        END IF;
        IF available_capacity IS NULL THEN
            available_capacity = 0;
        END IF;
        
        -- Create single day data object
        single_day_data = json_build_object(
            'id', LOWER(day_name) || '_' || day_counter,
            'name', day_name,
            'date', formatted_date,
            'totalSlots', total_slots,
            'available', available_capacity::text || '/' || total_capacity::text,
            'totalCapacity', total_capacity,
            'availableCapacity', available_capacity,
            'dayDate', current_date_,
            'is_available', CASE WHEN available_capacity > 0 THEN true ELSE false END
        );
        
        -- Add to days array
        days_data = array_append(days_data, single_day_data);
    END LOOP;

    -- Convert array to JSON
    resp_data = array_to_json(days_data);
    
    status = true;
    message = 'success';

    RETURN json_build_object(
        'status', status,
        'message', message,
        'data', resp_data
    );

EXCEPTION
    WHEN OTHERS THEN
        status = false;
        message = 'Database error: ' || SQLERRM;
        resp_data = '[]'::json;
        
        RETURN json_build_object(
            'status', status,
            'message', message,
            'data', resp_data
        );
END;
$function$;

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Select, Button, Spin, Col } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import BulkUploader from '../../components/wify-utils/BulkUploader';
import BookingMode from './BookingMode';
import ConfigHelpers from '../../util/ConfigHelpers';

const { Option } = Select;

const BulkCreationCollapse = (props) => {
    const [selectedBulkPrvdr, setSelectedBulkPrvdr] = useState(null);
    const [capacityData, setCapacityData] = useState(undefined);
    const [bookingModeModal, setBookingModeModal] = useState(false);
    const [loadingCapacity, setLoadingCapacity] = useState(false);

    useEffect(() => {
        // Auto-select service provider if user is a service provider
        if (ConfigHelpers.isServiceProvider()) {
            const currentUserOrgId =
                ConfigHelpers.getUserDetailsInLocalStorage()?.org?.id;
            if (currentUserOrgId) {
                setSelectedBulkPrvdr(currentUserOrgId);
                fetchCapcityData(currentUserOrgId);
            }
        } else {
            // Check for default provider configuration
            const defaultProvider = props.srvcConfigData?.srvc_default_provider;
            if (
                defaultProvider &&
                defaultProvider !== '' &&
                defaultProvider !== null
            ) {
                // Auto-select default provider if configured
                setSelectedBulkPrvdr(parseInt(defaultProvider));
                fetchCapcityData(parseInt(defaultProvider));
            } else {
                // Auto-select "No Provider" if no default provider is configured
                setSelectedBulkPrvdr('no_provider');
            }
        }
    }, [props.srvcConfigData]);

    const fetchCapcityData = (prvdrId) => {
        // Skip capacity API call for "no_provider" selection
        if (prvdrId === 'no_provider') {
            setLoadingCapacity(false);
            setCapacityData(undefined);
            return;
        }

        setLoadingCapacity(true);
        let params = {
            srvc_prvdr_id: prvdrId,
        };

        const onComplete = (resp) => {
            setLoadingCapacity(false);
            setCapacityData(resp.data);
        };

        const onError = (error) => {
            setLoadingCapacity(false);
            setCapacityData(undefined);
        };

        // Use the http_utils from props or import it
        props.httpUtils.performGetCall(
            '/booking/capacity',
            params,
            onComplete,
            onError
        );
    };

    const getAllSrvcPrvdrs = () => {
        const { allSrvcPrvdrs, srvcConfigData } = props;

        // Get only allowed providers
        const allowedPrvdrs = allSrvcPrvdrs.filter((prvdr) =>
            srvcConfigData?.srvc_possible_prvdrs.includes(prvdr.value)
        );

        // Add "No Provider" option
        const noProviderOption = {
            value: 'no_provider',
            label: 'No Service Provider',
        };

        const defaultProviderId = parseInt(
            srvcConfigData?.srvc_default_provider
        );

        // Check if default provider exists in allSrvcPrvdrs
        const defaultPrvdr =
            allSrvcPrvdrs.find((p) => p.value === defaultProviderId) || null;

        // If default provider is valid but not in allowed list, add it manually
        const isDefaultInList = allowedPrvdrs.some(
            (prvdr) => prvdr.value === defaultProviderId
        );

        if (defaultPrvdr && !isDefaultInList) {
            allowedPrvdrs.push(defaultPrvdr);
        }

        return [noProviderOption, ...allowedPrvdrs];
    };

    const {
        TMS250729178325,
        srvcConfigData,
        submitUrl,
        dataProto,
        orgSettingsData,
        onDataModified,
        srvcTypeId,
        viewData,
    } = props;

    return (
        <Col xs={24} className="gx-my-1">
            <Collapse>
                <Collapse.Panel
                    header={
                        <span className="gx-text-primary">
                            <UploadOutlined className="gx-mr-2" />
                            Click here for Bulk creation
                        </span>
                    }
                >
                    <div>
                        {TMS250729178325 &&
                        srvcConfigData?.srvc_possible_prvdrs?.length > 0 ? (
                            <div>
                                <div className="gx-mb-3">
                                    <label>Select Service Provider:</label>
                                    <Select
                                        className="gx-mt-2 gx-w-100"
                                        placeholder="Choose a service provider"
                                        value={selectedBulkPrvdr}
                                        disabled={ConfigHelpers.isServiceProvider()}
                                        onChange={(value) => {
                                            setSelectedBulkPrvdr(value);
                                            // Fetch capacity data for all selections (including "no_provider")
                                            fetchCapcityData(value);
                                        }}
                                    >
                                        {getAllSrvcPrvdrs()?.map((prvdr) => (
                                            <Option
                                                key={prvdr.value}
                                                value={prvdr.value}
                                            >
                                                {prvdr.label}
                                            </Option>
                                        ))}
                                    </Select>
                                    {loadingCapacity ? (
                                        <Spin />
                                    ) : (
                                        <>
                                            {selectedBulkPrvdr ===
                                            'no_provider' ? (
                                                // Show BulkUploader directly for "no_provider" selection
                                                <BulkUploader
                                                    onDataModified={
                                                        onDataModified
                                                    }
                                                    submitUrl={submitUrl}
                                                    dataProto={dataProto}
                                                    orgSettingsData={
                                                        orgSettingsData
                                                    }
                                                    timeFormatMsg
                                                    noProviderMode={true}
                                                />
                                            ) : (
                                                <>
                                                    {capacityData?.enable_capacity_module ? (
                                                        <Button
                                                            className="gx-mt-3"
                                                            type="primary"
                                                            onClick={() => {
                                                                setBookingModeModal(
                                                                    true
                                                                );
                                                            }}
                                                        >
                                                            Check Booking Mode
                                                        </Button>
                                                    ) : (
                                                        <>
                                                            {selectedBulkPrvdr && (
                                                                <BulkUploader
                                                                    onDataModified={
                                                                        onDataModified
                                                                    }
                                                                    submitUrl={
                                                                        submitUrl
                                                                    }
                                                                    dataProto={
                                                                        dataProto
                                                                    }
                                                                    orgSettingsData={
                                                                        orgSettingsData
                                                                    }
                                                                    timeFormatMsg
                                                                />
                                                            )}
                                                        </>
                                                    )}
                                                </>
                                            )}
                                        </>
                                    )}
                                </div>
                            </div>
                        ) : (
                            <BulkUploader
                                onDataModified={onDataModified}
                                submitUrl={submitUrl}
                                dataProto={dataProto}
                                orgSettingsData={orgSettingsData}
                                timeFormatMsg
                            />
                        )}
                    </div>
                    <>
                        {bookingModeModal && (
                            <BookingMode
                                showBulkBookingModal={bookingModeModal}
                                onClose={() => {
                                    setBookingModeModal(false);
                                }}
                                capacityData={capacityData}
                                selectedPrvdr={selectedBulkPrvdr}
                                srvcTypeId={srvcTypeId}
                                dataProto={dataProto}
                                orgSettingsData={orgSettingsData}
                                onDataModified={onDataModified}
                                vertical_id={viewData?.sp_config_data[0]?.db_id}
                            />
                        )}
                    </>
                </Collapse.Panel>
            </Collapse>
        </Col>
    );
};

export default BulkCreationCollapse;

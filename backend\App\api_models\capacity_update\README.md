# Capacity Update Module

## Overview

This module handles the synchronization of capacity data between the TMS system and the Bookings service.

## Key Components

- **capacity_update_model.js**: Core model that manages capacity updates across organizations
- **utils.js**: Utility functions for data transformation and Lambda interactions

## Features

- Batch processing of capacity data
- Asynchronous job queuing for resource combinations
- Organization-specific Lambda ARN support
- Automatic retries with exponential backoff
- Detailed logging and error reporting

## Usage

The module is primarily triggered via a cron job at:

```
/trigger-capacity-update/${CAPACITY_UPDATE_CRON_KEY}
```

### Query Parameters

- `start_date` (optional): Start date for capacity processing in YYYY-MM-DD format. Defaults to current date.
- `end_date` (optional): End date for capacity processing in YYYY-MM-DD format. Defaults to 7 days from current date.

**Example:**

```
/trigger-capacity-update/${CAPACITY_UPDATE_CRON_KEY}?start_date=2024-12-04&end_date=2024-12-10
```

**Note:** Date range is limited to maximum 30 days to prevent excessive job creation.

## Processing Flow

1. Fetch organizations with capacity module enabled
2. Generate date range based on query parameters (current date to 7 days ahead by default)
3. For each organization and each date in the range, create resource combination jobs (Queue: `CRON_CAPACITY_UPDATE`)
4. For each resource combination, fetch and process capacity data (Queue: `CRON_CAPACITY_RESOURCE_UPDATE`)
    - Fetches capacity data for the specific resource combination and date
    - Processes data in batches of 5,000 records using database functions
    - Directly inserts/updates capacity data in the database

## Performance

- Handles up to 50,000 records per organization
- Processes in batches of 5,000 records using optimized database functions
- Uses dedicated queues for organization and resource-level processing

## Testing

The capacity update module uses database-based operations for processing capacity data. Testing should focus on:

- Database function validation (`tms_ace_bulk_upsert_capacity_data`)
- Queue processing functionality
- Data transformation accuracy
- Error handling and retry mechanisms

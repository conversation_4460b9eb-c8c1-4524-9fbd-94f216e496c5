CREATE OR REPLACE FUNCTION public.tms_ace_get_slotwise_capacity_data(form_data json)
RETURNS json
LANGUAGE plpgsql
AS $function$
DECLARE
    status bool;
    message text;
    resp_data json;
    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;
    city_name_ text;
    start_date_ date;
    end_date_ date;
    current_date_ date;
    days_data json[];
    single_day_data json;
    day_name text;
    formatted_date text;
    total_capacity integer;
    available_capacity integer;
    day_counter integer;
    time_slots_data json[];
    single_slot_data json;
    slot_counter integer;
    slot_start_time time;
    slot_end_time time;
    slot_available integer;
    slot_total integer;
    org_timezone text;
    vertical_id_ int;
    service_prvdr_ int;
    total_slots integer; 
BEGIN
    status = false;
    message = 'Internal_error';
    resp_data = '[]'::json;

    -- Extract parameters from form_data
    org_id_ = (form_data->>'org_id')::integer;   
    usr_id_ = form_data->>'usr_id';
    ip_address_ = form_data->>'ip_address';
    user_agent_ = form_data->>'user_agent';
    city_name_ = form_data->>'city_name';
    org_timezone = tms_hlpr_get_org_timezone(org_id_);
    vertical_id_ = form_data->>'vertical_id';
    service_prvdr_ = form_data->>'service_prvdr';

    -- Set date range for next 7 days starting from today
    start_date_ = CURRENT_DATE;
    end_date_ = CURRENT_DATE + INTERVAL '6 days';
    
    days_data = ARRAY[]::json[];
    day_counter = 0;

    -- Loop through each day
    FOR current_date_ IN SELECT generate_series(start_date_, end_date_, '1 day')::date
    LOOP
        day_counter = day_counter + 1;
        
        -- Get day name and formatted date
        day_name = TRIM(TO_CHAR(current_date_, 'Day'));
        formatted_date = TO_CHAR(current_date_, 'Month DD, YYYY');

        -- Get total and available capacity for this day
        SELECT
            COUNT(DISTINCT cap.start_time), 
            COALESCE(SUM(cap.total_capacity), 0),
            COALESCE(SUM(cap.available_capacity), 0)
         INTO 
            total_slots,
            total_capacity,
            available_capacity
         FROM public.cl_tx_capacity cap
        INNER JOIN public.cl_tx_vertical_srvc_hubs hub 
           ON hub.org_id = service_prvdr_
          AND hub.is_active = true
          AND cap.resource_id LIKE service_prvdr_::text || '_' || vertical_id_::text || '_%_' || hub.id::text
        WHERE cap.usr_tmzone_day = current_date_;

        -- Defaults if NULL
        IF total_capacity IS NULL THEN
            total_capacity = 0;
        END IF;
        IF available_capacity IS NULL THEN
            available_capacity = 0;
        END IF;

        time_slots_data = ARRAY[]::json[];
        slot_counter = 0;

        -- 🔄 Dynamic time slot generation from capacity table
        FOR slot_start_time, slot_end_time IN
            SELECT DISTINCT cap.start_time::time, cap.end_time::time
             FROM public.cl_tx_capacity cap
            INNER JOIN public.cl_tx_vertical_srvc_hubs hub 
               ON hub.org_id = service_prvdr_
              AND hub.is_active = true
              AND cap.resource_id LIKE service_prvdr_::text || '_' || vertical_id_::text || '_%_' || hub.id::text
            WHERE cap.usr_tmzone_day = current_date_

        LOOP
            slot_counter = slot_counter + 1;

            -- Get slot-specific capacity
            SELECT 
                COALESCE(SUM(cap.available_capacity), 0),
                COALESCE(SUM(cap.total_capacity), 0)
             INTO 
                slot_available,
                slot_total
             FROM public.cl_tx_capacity cap
            INNER JOIN public.cl_tx_vertical_srvc_hubs hub 
               ON hub.org_id = org_id_
              AND hub.is_active = true
              AND cap.resource_id LIKE org_id_::text || '_' || vertical_id_::text || '_%_' || hub.id::text
            WHERE cap.usr_tmzone_day = current_date_
              AND cap.start_time::time = slot_start_time
              AND cap.end_time::time = slot_end_time;

            -- Fallback if slot data is missing but total is present
            IF slot_total = 0 AND total_capacity > 0 THEN
                slot_total = total_capacity / GREATEST(1, slot_counter);
                slot_available = available_capacity / GREATEST(1, slot_counter);
            END IF;

            -- Build slot JSON
            single_slot_data = json_build_object(
                'id', 'slot_' || day_counter || '_' || slot_counter,
                'timeRange', TO_CHAR((current_date_ + slot_start_time)::timestamp AT TIME ZONE 'UTC' AT TIME ZONE org_timezone, 'hh12:miam') || 
                            ' - ' || TO_CHAR((current_date_ + slot_end_time)::timestamp AT TIME ZONE 'UTC' AT TIME ZONE org_timezone, 'hh12:miam'),
                'startTime', TO_CHAR((current_date_ + slot_start_time)::timestamp AT TIME ZONE 'UTC' AT TIME ZONE org_timezone, 'hh12:miam'),
                'endTime', TO_CHAR((current_date_ + slot_end_time)::timestamp AT TIME ZONE 'UTC' AT TIME ZONE org_timezone, 'hh12:miam'),
                'available', slot_available::text || '/' || slot_total::text,
                'availableCapacity', slot_available,
                'totalCapacity', slot_total
                
            );

            time_slots_data = array_append(time_slots_data, single_slot_data);
        END LOOP;

        -- Build day-level JSON
        single_day_data = json_build_object(
            'id', LOWER(day_name) || '_' || day_counter,
            'name', day_name,
            'date', formatted_date,
            'available', available_capacity::text || '/' || total_capacity::text,
            'totalCapacity', total_capacity,
            'availableCapacity', available_capacity,
            'dayDate', current_date_,
            'timeSlots', array_to_json(time_slots_data),
            'is_available', CASE WHEN available_capacity > 0 THEN true ELSE false END,
            'totalSlots', total_slots
        );

        days_data = array_append(days_data, single_day_data);
    END LOOP;

    -- Final JSON response
    resp_data = array_to_json(days_data);
    status = true;
    message = 'success';

    RETURN json_build_object(
        'status', status,
        'message', message,
        'data', resp_data
    );

EXCEPTION
    WHEN OTHERS THEN
        status = false;
        message = 'Database error: ' || SQLERRM;
        resp_data = '[]'::json;

        RETURN json_build_object(
            'status', status,
            'message', message,
            'data', resp_data
        );
END;
$function$;

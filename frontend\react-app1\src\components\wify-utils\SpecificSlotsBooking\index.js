import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Spin, Button, Typography } from 'antd';
import {
    CalendarOutlined,
    ClockCircleOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    CheckCircleFilled,
    CloseCircleFilled,
} from '@ant-design/icons';
import BulkUploader from '../BulkUploader';
import RemoteSourceSelect from '../../../components/wify-utils/RemoteSourceSelect';
import http_utils from '../../../util/http_utils';
import './index.css';
import Progress from 'antd/es/progress';
import { BsChevronLeft, BsChevronRight } from 'react-icons/bs';
import BulkUploaderV2 from '../BulkUploaderV2';

const { Title, Text } = Typography;
const cardsToShow = 4;

const SpecificSlotsBookingComponent = ({
    selectedPrvdr,
    onDataModified,
    srvcTypeId,
    dataProto,
    orgSettingsData,
    vertical_id,
}) => {
    const [loading, setLoading] = useState(false);
    const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
    const [daysData, setDaysData] = useState([]);
    const [slotsLoading, setSlotsLoading] = useState(false);
    const [selectedDay, setSelectedDay] = useState(null);
    const [showSingleDay, setShowSingleDay] = useState(false);
    const [error, setError] = useState(undefined);
    const [startIndex, setStartIndex] = useState(0);
    const [scrollIndex, setScrollIndex] = useState(0);

    useEffect(() => {
        initviewData();
    }, []);

    const initviewData = () => {
        if (loading) return;
        setLoading(true);
        setDaysData([]);
        setError(undefined);
        let params = {
            service_prvdr: selectedPrvdr,
            vertical_id: vertical_id,
        };
        const onComplete = (resp) => {
            setDaysData(resp.data);
            setLoading(false);
        };

        const onError = (error) => {
            setDaysData([]);
            setError(http_utils.decodeErrorToMessage(error));
            setLoading(false);
        };

        http_utils.performGetCall(
            '/booking/slotwise-slots-data',
            params,
            onComplete,
            onError
        );
    };

    // Render time slots for a specific day in new UI format
    const renderTimeSlots = (day) => {
        if (!day.timeSlots || day.timeSlots.length === 0) {
            return (
                <div className="gx-text-center gx-text-gray gx-fs-md gx-p-1">
                    No time slots available
                </div>
            );
        }

        return (
            <div>
                {day.timeSlots.map((slot) => {
                    const isSelected =
                        selectedTimeSlot?.dayId === day.id &&
                        selectedTimeSlot?.slotId === slot.id;

                    return (
                        <div
                            key={slot.id}
                            className="gx-d-flex gx-align-items-center gx-fs-md gx-mb-1"
                        >
                            <span className="gx-mr-2">
                                {slot.available ? (
                                    <CheckCircleFilled
                                        style={{ color: '#1890ff' }}
                                    />
                                ) : (
                                    <CloseCircleFilled
                                        style={{ color: '#f5222d' }}
                                    />
                                )}
                            </span>
                            <span>{slot.timeRange}</span>
                        </div>
                    );
                })}
            </div>
        );
    };

    // Render single day view with new UI

    const handleDaySelect = (dayId) => {
        setSelectedDay(dayId);
    };

    const handleNextScroll = () => {
        setScrollIndex(scrollIndex + 1);
    };

    const handlePrevScroll = () => {
        setScrollIndex(scrollIndex - 1);
    };

    const getEnhancedDataProto = () => {
        const enhancedProto = [...dataProto];

        if (selectedPrvdr) {
            enhancedProto.selectedPrvdr = selectedPrvdr;
        }
        if (selectedDay) {
            enhancedProto.selectedDay = selectedDay.dayDate; // Add selected day data
        }

        return enhancedProto;
    };

    return (
        <div className="gx-mt-3">
            <>
                {loading ? (
                    <div className="gx-text-center">
                        <Spin size="large" />
                    </div>
                ) : (
                    <>
                        {/* Horizontal Scrollable Row */}
                        <div className=" gx-align-items-center gx-justify-content-center gx-position-relative">
                            {/* Left Arrow Button */}
                            <Button
                                icon={<BsChevronLeft />}
                                className="wy-sb-nav-btn wy-sb-prev-nav-btn  wy-day-nav-btn-left"
                                onClick={handlePrevScroll}
                                disabled={scrollIndex === 0}
                                shape="circle"
                            />

                            {/* Row of cards */}
                            <Row
                                gutter={[16, 16]}
                                className="wy-day-card-container"
                            >
                                {daysData
                                    .slice(
                                        scrollIndex,
                                        scrollIndex + cardsToShow
                                    )
                                    .map((day) => (
                                        <Col xs={24} sm={6} key={day.id}>
                                            <Card
                                                hoverable={day.is_available}
                                                className={`wy-day-card ${
                                                    selectedDay === day
                                                        ? 'selected'
                                                        : ''
                                                } ${day.is_available ? '' : 'unavailable'}`}
                                                onClick={() => {
                                                    if (day.is_available) {
                                                        handleDaySelect(day);
                                                    }
                                                }}
                                            >
                                                <div className="gx-text-align-left gx-p-2">
                                                    <div className="gx-d-flex gx-align-items-center gx-mb-2">
                                                        <CalendarOutlined className="gx-mr-2 gx-text-primary gx-fs-xxl" />
                                                        <div>
                                                            <div className="gx-fs-md gx-font-weight-semi-bold">
                                                                {day.name}
                                                            </div>
                                                            <div className="gx-fs-sm gx-text-gray">
                                                                {day.date}
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div className="gx-mb-1 gx-fs-sm">
                                                        <div>
                                                            Total Slots:{' '}
                                                            {day.totalSlots}
                                                        </div>
                                                        <div>
                                                            Available:{' '}
                                                            {day.available}
                                                        </div>
                                                    </div>

                                                    <Progress
                                                        percent={Math.round(
                                                            (day.availableCapacity /
                                                                day.totalCapacity) *
                                                                100
                                                        )}
                                                        size="small"
                                                        showInfo={false}
                                                        strokeColor="#1890ff"
                                                        trailColor="#eee"
                                                    />
                                                    {renderTimeSlots(day)}
                                                </div>
                                            </Card>
                                        </Col>
                                    ))}
                            </Row>

                            {/* Right Arrow Button */}
                            <Button
                                icon={<BsChevronRight />}
                                className="wy-sb-nav-btn wy-sb-next-nav-btn wy-day-nav-btn-right"
                                onClick={handleNextScroll}
                                shape="circle"
                                disabled={
                                    scrollIndex + cardsToShow >= daysData.length
                                }
                            />
                        </div>

                        {/* Bulk uploader V2 */}
                        {selectedDay && (
                            <div>
                                <BulkUploaderV2
                                    onDataModified={onDataModified}
                                    srvcTypeId={srvcTypeId}
                                    dataProto={getEnhancedDataProto()}
                                    orgSettingsData={orgSettingsData}
                                    selectedSlotData={
                                        selectedDay?.timeSlots || []
                                    }
                                    vertical_id={vertical_id}
                                />
                            </div>
                        )}
                    </>
                )}
            </>
        </div>
    );
};

export default SpecificSlotsBookingComponent;
